package com.ybmmarket20.activity;

import android.graphics.Rect;
import android.os.Bundle;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CheckOrderListBean;
import com.ybmmarket20.bean.CheckOrderRowsBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.SuggestPopWindow;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/*
 * 选择申请订单搜索页
 * */
@Router({"selectapplyforordersearch"})
public class SelectApplyForOrderSearchActivity extends BaseActivity {

    @Bind(R.id.title_left_search)
    ImageView titleLeftSearch;
    @Bind(R.id.title_et)
    EditText titleEt;
    @Bind(R.id.iv_clear)
    ImageView ivClear;
    @Bind(R.id.rel_search)
    RelativeLayout relSearch;
    @Bind(R.id.title_right_btn)
    TextView titleRightBtn;
    @Bind(R.id.ll_title)
    LinearLayout llTitle;
    @Bind(R.id.search_product_list_view)
    CommonRecyclerView searchProductListView;
    @Bind(R.id.brand_ctl)
    CoordinatorLayout brandCtl;
    @Bind(R.id.ly_search)
    LinearLayout lySearch;
    @Bind(R.id.rl_root)
    RelativeLayout rlRoot;

    private SuggestPopWindow suggestPopWindow;
    private String keyword = "";//关键词过来的
    private boolean isSuggest = true;
    private boolean selectId;
    private String suggestStr = "";
    View view;
    private int page = 0;
    private int pageSize = 10;
    private List<CheckOrderRowsBean> comments = new ArrayList<>();
    private SelectApplyForOrderAdapter adapter;
    private int bottom = com.ybm.app.utils.UiUtils.dp2px(6);

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN | WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_select_apply_for_order_search;
    }

    @Override
    protected void initData() {

        suggestPopWindow = new SuggestPopWindow(this, AppNetConfig.SUGGEST, new RequestParams(), findViewById(R.id.ll_title));
        suggestPopWindow.setItemClickListener((str, id, position) -> {
            if (titleEt == null) {
                return;
            }
            hideSoftInput();
            keyword = str;
            getSearchData(page = 0);
            isSuggest = false;
            titleEt.setText(str);
            titleEt.setSelection(str.length());
        });
        suggestPopWindow.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                isScroll(event);
                return false;
            }
        });

        titleEt.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS | InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS);
        titleEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString().trim())) {
                    ivClear.setVisibility(View.VISIBLE);
                } else {
                    ivClear.setVisibility(View.INVISIBLE);
                }
                if (!isSuggest || TextUtils.isEmpty(s.toString().trim())) {
                    if (selectId) {
                        selectId = false;
                        return;
                    }
                    isSuggest = true;
                    searchProductListView.setVisibility(View.VISIBLE);
                    return;
                }
                suggestStr = s.toString();
                suggestPopWindow.cancelHandler(false);
                suggestPopWindow.suggest(suggestStr);

            }
        });
        titleEt.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_SEND) {
                    String strT = titleEt.getText().toString().trim();
                    String strH = titleEt.getHint().toString().trim();
                    if (TextUtils.isEmpty(strT) && !TextUtils.isEmpty(strH) &&
                            !getResources().getString(R.string.search_hint).equals(titleEt.getHint().toString())) {
                        keyword = strH;
                    } else {
                        keyword = strT;
                    }
                    titleEt.setHint(R.string.search_hint);
                    titleEt.setText(keyword);
                    titleEt.setSelection(keyword.length());
                    getSearchData(page = 0);
                    return true;
                }
                return false;
            }
        });

        titleEt.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {

            //当键盘弹出隐藏的时候会 调用此方法。
            @Override
            public void onGlobalLayout() {

                int height = UiUtils.getNavigationBarHeight(SelectApplyForOrderSearchActivity.this);


                Rect r = new Rect();
                //获取当前界面可视部分
                SelectApplyForOrderSearchActivity.this.getWindow().getDecorView().getWindowVisibleDisplayFrame(r);

                //获取屏幕的高度
                int screenHeight = SelectApplyForOrderSearchActivity.this.getWindow().getDecorView().getRootView().getHeight();

                //此处就是用来获取键盘的高度的， 在键盘没有弹出的时候 此高度为0 键盘弹出的时候为一个正数
                int heightDifference = screenHeight - r.bottom - height;
                showAViewOverKeyBoard(heightDifference);

            }

        });

        titleEt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(titleEt.getText())) {
                    suggestPopWindow.cancelHandler(false);
                    suggestPopWindow.suggest(titleEt.getText().toString().trim());
                }
                titleRightBtn.setVisibility(View.VISIBLE);
            }
        });

        adapter = new SelectApplyForOrderAdapter(R.layout.item_select_apply_for_order, comments);

        searchProductListView.setEnabled(false);
        searchProductListView.setAdapter(adapter);
        searchProductListView.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "暂无相关订单");
        adapter.openLoadMore(10, true);
        searchProductListView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.bottom = bottom;
            }
        });
        searchProductListView.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                // getRefreshResponse();
            }

            @Override
            public void onLoadMore() {
                getSearchData(page);
            }
        });

    }

    //语音搜索
    private void showAViewOverKeyBoard(int heightDifference) {
        if (rlRoot == null) {
            return;
        }
        if (heightDifference > 0) {//显示
            if (view == null) {//第一次显示的时候创建  只创建一次
                view = View.inflate(this, R.layout.search_product_voice, null);
                RelativeLayout.LayoutParams loginlayoutParams = new RelativeLayout.LayoutParams(-1, -2);
                loginlayoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                loginlayoutParams.bottomMargin = heightDifference;
                rlRoot.addView(view, loginlayoutParams);
                view.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //语音搜索
                        hideSoftInput(titleEt);
                        RoutersUtils.open("ybmpage://searchvoiceactivity");
                        finish();
                    }
                });
            }
            view.setVisibility(View.VISIBLE);
        } else {//隐藏
            if (view != null) {
                view.setVisibility(View.GONE);
            }
        }
    }

    public void getSearchData(int page) {
        if (rlRoot == null) {
            return;
        }
        if (TextUtils.isEmpty(keyword)) {
            ToastUtils.showShort("搜索内容不能为空");
            return;
        }

        suggestPopWindow.cancelHandler(true);
        popDismiss();
        //隐藏软键盘
        hideSoftInput();
        showProgress();
        HttpManager.getInstance().post(AppNetConfig.ESCORT_GET_ORDER_DETAIL_ESCORT, getParams(page), new BaseResponse<CheckOrderListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CheckOrderListBean> obj, CheckOrderListBean rowsBeans) {

                completion();
                dismissProgress();
                hideSoftInput();
                titleRightBtn.setVisibility(View.VISIBLE);
                if (obj != null) {
                    if (obj.isSuccess()) {
                        if (rowsBeans != null) {

                            if (rowsBeans.getRows() != null && rowsBeans.getRows().size() > 0) {
                                if (page <= 0) {
                                    SelectApplyForOrderSearchActivity.this.page = 1;
                                } else {
                                    SelectApplyForOrderSearchActivity.this.page++;
                                }
                            }
                            if (page <= 0) {

                                if (comments == null) {
                                    comments = new ArrayList<>();
                                }
                                comments.clear();
                                if (comments.size() <= 0 && rowsBeans.getRows() != null) {
                                    comments.addAll(rowsBeans.getRows());
                                } else {
                                    if (rowsBeans.getRows() == null || rowsBeans.getRows().isEmpty()) {

                                    } else {

                                        comments.addAll(0, rowsBeans.getRows());
                                    }
                                }
                                adapter.setNewData(comments);
                                adapter.notifyDataChangedAfterLoadMore(comments.size() >= pageSize);
                            } else {
                                if (rowsBeans.getRows() == null || rowsBeans.getRows().size() <= 0) {
                                    adapter.notifyDataChangedAfterLoadMore(false);
                                } else {
                                    comments.addAll(rowsBeans.getRows());
                                    adapter.setNewData(comments);
                                    adapter.notifyDataChangedAfterLoadMore(rowsBeans.getRows().size() >= pageSize);
                                }
                            }
                        }
                        popDismiss();
                    }
                }
                popDismiss();
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                completion();
                popDismiss();
            }
        });
    }

    private void completion() {
        try {
            if (searchProductListView != null) {
                searchProductListView.setRefreshing(false);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void popDismiss() {
        if (suggestPopWindow != null) {
            suggestPopWindow.dismiss();
        }
    }

    private RequestParams getParams(int page) {
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("limit", pageSize + "");

        if (!TextUtils.isEmpty(keyword)) {
            params.put("productName", keyword);
        }

        params.put("offset", page + "");

        return params;
    }

    @OnClick({R.id.title_left_search, R.id.iv_clear})
    public void clickTab(View view) {
        switch (view.getId()) {
            //返回键
            case R.id.title_left_search:
                hideSoftInput();
                finish();
                break;
            //清除搜索框
            case R.id.iv_clear:
                titleEt.setText("");
                titleRightBtn.setVisibility(View.VISIBLE);
                showSoftInput();
                break;
        }
    }

}
