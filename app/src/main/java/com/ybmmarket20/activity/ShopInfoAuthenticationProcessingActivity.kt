package com.ybmmarket20.activity

import android.view.View
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.bean.AuthenticationProcessingInfo
import com.ybmmarket20.utils.analysis.XyyIoUtil
import kotlinx.android.synthetic.main.activity_associated_shop_authentication_processing.*

/**
 * 店铺信息审核中
 */
@Router("shopinfoauthenticationprocessing")
class ShopInfoAuthenticationProcessingActivity: AssociatedShopAuthenticationProcessingActivity() {

    override fun initData() {
        super.initData()
        setTitle("店铺信息审核中")
        hintTv.text = "*您添加的店铺信息，预计24小时内完成审核，请您耐心等待，审核通过后可登入药帮忙App"
    }

    override fun setData(info: AuthenticationProcessingInfo) {
        super.setData(info)
        etCompanyType.setText(info.customerType)
        etNumber.setText(info.businessLicenseNo)
        groupCompanyType.visibility = View.VISIBLE
    }

    override fun quiteBtnClick() {
        super.quiteBtnClick()
        XyyIoUtil.trackForAccount("action_poiAudit_logout_click")
    }

    override fun selectOtherShop() {
        super.selectOtherShop()
        XyyIoUtil.trackForAccount("action_poiAudit_selectPoi_click")
    }
}