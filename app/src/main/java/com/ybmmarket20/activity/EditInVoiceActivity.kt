package com.ybmmarket20.activity

import androidx.activity.viewModels
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.common.vm.BaseVMActivity
import com.ybmmarket20.databinding.ActivityEditInvoiceBinding
import com.ybmmarket20.viewmodel.EditInvoiceViewModel

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/9/18 15:40
 *    desc   :
 */
@Router("editInVoiceActivity")
class EditInVoiceActivity :
    BaseVMActivity<ActivityEditInvoiceBinding, EditInvoiceViewModel>(ActivityEditInvoiceBinding::inflate) {
    override val viewModel: EditInvoiceViewModel by viewModels()
    override fun initData() {
        setTitle("修改发票信息")

    }

}