package com.ybmmarket20.business.order.adapter

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.business.order.bean.OrderFilterItemBean

/**
 * <AUTHOR>
 * @desc    订单列表搜索筛选项：单选
 */
class OrderFilterAdapter(val resId: Int) : YBMBaseAdapter<OrderFilterItemBean>(resId,null) {
    var itemId = 0
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: OrderFilterItemBean) {
        val textView = baseViewHolder.getView<TextView>(R.id.text_view_time_range)
        textView.text = t.itemText
        textView.isSelected = t.itemId == itemId
    }
}