package com.ybmmarket20.business.order.adapter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.ybmmarket20.home.MainOneClickRestockingChildFragment;

import java.util.List;

public class RestockingPagerAdapter extends FragmentPagerAdapter {

    private List<MainOneClickRestockingChildFragment> fragments;

    public RestockingPagerAdapter(FragmentManager fm, List<MainOneClickRestockingChildFragment> fragments) {
        super(fm);
        this.fragments=fragments;
    }


    @Override
    public int getCount() {
        return fragments.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return fragments.get(position).getTitle();
    }

    @Override
    public Fragment getItem(int position) {
        return fragments.get(position);
    }


}
