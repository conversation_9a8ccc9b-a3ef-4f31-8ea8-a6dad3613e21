package com.ybmmarket20.business.order.ui

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R
import com.ybmmarket20.utils.SpUtil

/**
 * <AUTHOR>
 * @desc    订单筛选引导页
 */
class OrderGuideFilterView @JvmOverloads constructor(val mContext: Context, attrs: AttributeSet? = null, def: Int = 0) :
    ConstraintLayout(mContext, attrs, def) {
    init {
        LayoutInflater.from(context).inflate(R.layout.guide_order_filter, this)
    }
    companion object{
        const val GUIDE_ORDER_FILTER = "guide_order_filter"
    }

    /**
     * 展示引导
     */
    fun show(){
        val shownCount = SpUtil.readInt(GUIDE_ORDER_FILTER, 0)
        if(shownCount == 0){
            if(mContext is Activity){
                val decorView = mContext.window.decorView as ViewGroup
                findViewById<View>(R.id.cl_root).setOnClickListener {
                    decorView.removeView(this)
                }
                decorView.addView(this)
                SpUtil.writeInt(GUIDE_ORDER_FILTER,1)
            }
        }
    }
}