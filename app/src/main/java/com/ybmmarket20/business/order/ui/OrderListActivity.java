package com.ybmmarket20.business.order.ui;

import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;


import com.flyco.tablayout.SlidingTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.LoginActivity;
import com.ybmmarket20.activity.SearchOrderActivity;
import com.ybmmarket20.business.order.adapter.OrderListPagerAdapter;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.NoScrollViewPager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import butterknife.Bind;
import butterknife.OnClick;
import com.ybmmarket20.viewmodel.SwitchViewModel;
import com.ybmmarketkotlin.feature.collect.CollectActivity;

/**
 * 我的订单列表
 * 订单状态：2017-4-28 更新增加完整订单状态
 * 1 审核中，订单审核中 action 再次购买，申请退款（全,只有详情有）
 * 2 配送中 action 再次购买，申请退款（选择对话框,只有详情有）
 * 3 已完成，action 再次购买，领取余额，申请退款（部分，只有详情有），可能还有查看退款功能（列表详情都有）
 * 4 取消 action 再次购买
 * 5 已删除 action 再次购买
 * 6 已拆单
 * 7 出库中  action 再次购买，申请退款（全,只有详情有）
 * 10 待支付，待付款 action 取消订单，立即支付
 * 21 已拒签 action 再次购买，申请退款（只有详情有）
 * 20 已送达 action 再次购买，确认收货，申请退货（部分，只有详情有）
 * 90 已申请退款 action 查看退款，再次购买
 * 91 退款完成 action 查看退款，再次购买
 * 92 退款失败 action 查看退款，再次购买
 */
//@Router({"myorderlist/:order_state", "myorderlist"})
public class OrderListActivity extends BaseActivity  {

    @Bind(R.id.vp)
    NoScrollViewPager vp;
    @Bind(R.id.ps_tab)
    SlidingTabLayout psTab;
    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.rl_search_order)
    RelativeLayout rlSearchOrder;


    private OrderListPagerAdapter adapter;

    private String order_state;
    private SwitchViewModel switchViewModel;

    @Override
    protected void initData() {
        if (!isLogin()) {
            //未登录
            gotoAtivity(LoginActivity.class, null);
            return;
        }
        initSwitchStatus();
        order_state = getIntent().getStringExtra(IntentCanst.ORDER_STATE);

        setTitle("我的订单");

        OrderListFragment fragment1 = OrderListFragment.getInstance(0, "全部");
        OrderListFragment fragment7 = OrderListFragment.getInstance(10, "待支付");
        OrderListFragment fragment2 = OrderListFragment.getInstance(1, "待配送");
        OrderListFragment fragment3 = OrderListFragment.getInstance(2, "配送中");
//        OrderListFragment fragment5 = OrderListFragment.getInstance(4, "待收货");
        OrderListFragment fragment4 = OrderListFragment.getInstance(3, "完成");
        OrderListFragment fragment6 = OrderListFragment.getInstance(101, "待评价");//101请勿动
        OrderListFragment fragment8 = OrderListFragment.getInstance(90, "退款/售后");


        ArrayList<OrderListFragment> orderListFragments = new ArrayList<>();
        orderListFragments.add(fragment1);
        orderListFragments.add(fragment7);
        orderListFragments.add(fragment2);
        orderListFragments.add(fragment3);
//        orderListFragments.add(fragment5);
        orderListFragments.add(fragment4);
        orderListFragments.add(fragment8);
        orderListFragments.add(fragment6);
        adapter = new OrderListPagerAdapter(getSupportFragmentManager(), orderListFragments);
        vp.setAdapter(adapter);
        vp.setScroll(false);
        psTab.setViewPager(vp);
        psTab.setIndicatorWidthEqualTitleHalf(true);
        psTab.addUnrelatedVpIndex(orderListFragments.indexOf(fragment8));
        psTab.setOnUnrelatedVpIndexCallback(index -> RoutersUtils.open("ybmpage://refundoraftersales"));

        int position;
        switch (order_state) {
            case "0":
                position = 0;
                break;
            case "10":
                position = 1;
                break;
            case "1":
                position = 2;

                break;
            case "2":
                position = 3;

                break;
//            case "4":
//                position = 4;
//
//                break;
            case "3":
                position = 4;

                break;
            case "101":
                position = 6;

                break;
            case "90":
                position = 5;
                break;

            default:
                position = 0;
                break;

        }

        vp.setCurrentItem(position, false);

        vp.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    private void initSwitchStatus() {
        switchViewModel = new ViewModelProvider(this).get(SwitchViewModel.class);
        switchViewModel.getPurchaseSwitchStatusLiveData().observe(this, integerBaseBean -> {
            if (integerBaseBean.isSuccess() && integerBaseBean.data != null && integerBaseBean.data.getPurchaseOrderSwitch() == 1) {
                findViewById(R.id.tv_right).setVisibility(View.VISIBLE);
            }
        });
        switchViewModel.getPurchaseSwitchStatus();
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_order_list;
    }

    @OnClick({R.id.iv_right, R.id.tv_right, R.id.rl_search_order})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.iv_right:
//                Intent intent = new Intent(this, SearchOrderActivity.class);
//                startActivity(intent);
                break;

            case R.id.tv_right:
                RoutersUtils.open("ybmpage://purchasereconciliation");
                HashMap<String, String> map = new HashMap<>();
                map.put("text", "我的账单");
                XyyIoUtil.track("page_OrderList_purchaseOrder", map);
                break;

            case R.id.rl_search_order:
                Intent intent = new Intent(this, SearchOrderActivity.class);
                startActivity(intent);
                break;
        }
    }
}
