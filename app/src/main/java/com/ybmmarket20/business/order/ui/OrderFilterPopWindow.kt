package com.ybmmarket20.business.order.ui

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseQuickAdapter.OnItemClickListener
import com.xyy.canary.utils.LogUtil
import com.ybmmarket20.R
import com.ybmmarket20.business.order.adapter.OrderFilterAdapter
import com.ybmmarket20.business.order.bean.OrderFilterItemBean
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.DateTimeUtil
import com.ybmmarket20.view.BaseBottomPopWindow
import com.ybmmarket20.view.picker.PickerManager
import java.util.Calendar
import java.util.Date
import kotlin.math.abs


/**
 * 普通品底部弹窗 列表加购底部弹窗
 */
class OrderFilterPopWindow(val activity: Activity) : BaseBottomPopWindow() {

    private var ivFilterClose: ImageView? = null
    private var rlvTime: RecyclerView? = null
    private var rlvFast: RecyclerView? = null
    private var tvStartTime: TextView? = null
    private var tvEndTime: TextView? = null
    private var tvClear: TextView? = null
    private var tvSure: TextView? = null
    var mStartTime: String = "" // 开始时间
    var mEndTime: String = ""   // 结束时间

    private var mTimeAdapter: OrderFilterAdapter? = null
    private var mFastAdapter: OrderFilterAdapter? = null

    override fun getLayoutId(): Int = R.layout.popwindow_order_filter

    override fun getLayoutParams(): LinearLayout.LayoutParams {
        return LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    override fun initView() {
        ivFilterClose = contentView.findViewById<ImageView>(R.id.ivFilterClose)
        rlvTime = contentView.findViewById<RecyclerView>(R.id.rlvFilterTime)
        rlvFast = contentView.findViewById<RecyclerView>(R.id.rlvFilterFast)
        tvStartTime = contentView.findViewById<TextView>(R.id.tvStartTime)
        tvEndTime = contentView.findViewById<TextView>(R.id.tvEndTime)
        tvClear = contentView.findViewById<TextView>(R.id.tvClear)
        tvSure = contentView.findViewById<TextView>(R.id.tvSure)
        ivFilterClose?.setOnClickListener {
            dismiss()
        }
        mTimeAdapter = OrderFilterAdapter(R.layout.item_order_filter)
        mTimeAdapter?.onItemClickListener = object : OnItemClickListener {
            override fun onItemClick(adt: BaseQuickAdapter<*, *>?, p1: View?, position: Int) {
                mTimeAdapter?.itemId = if ((adt?.getItem(position) as OrderFilterItemBean).itemId == mTimeAdapter?.itemId) {
                    0
                } else {
                    clearTime()
                    (adt?.getItem(position) as OrderFilterItemBean).itemId
                }
            }
        }
        rlvTime?.adapter = mTimeAdapter
        mFastAdapter = OrderFilterAdapter(R.layout.item_order_filter)
        mFastAdapter?.onItemClickListener = object : OnItemClickListener {
            override fun onItemClick(adt: BaseQuickAdapter<*, *>?, p1: View?, position: Int) {
                mFastAdapter?.itemId = if (mFastAdapter?.itemId == (adt?.getItem(position) as OrderFilterItemBean).itemId) {
                    0
                } else {
                    (adt?.getItem(position) as OrderFilterItemBean).itemId
                }
            }
        }
        rlvFast?.adapter = mFastAdapter
        tvStartTime?.setOnClickListener {
            showTimeSheetDialog(true)
        }
        tvEndTime?.setOnClickListener {
            showTimeSheetDialog(false)
        }
        tvClear?.setOnClickListener {
            mTimeAdapter?.itemId = 0
            mFastAdapter?.itemId = 0
            clearTime()
        }
    }

    fun dataListener(func: (timeItemId: Int, quickItemId: Int, startTime: String, endTime: String) -> Unit) {
        tvSure?.setOnClickListener {
            if (mTimeAdapter?.itemId == 0) {
                if (mStartTime.isNotEmpty() || mEndTime.isNotEmpty()) {
                    if (mStartTime.isEmpty()) {
                        ToastUtils.showShort("请选择起始时间")
                        return@setOnClickListener
                    }
                    if (mEndTime.isEmpty()) {
                        ToastUtils.showShort("请选择终止时间")
                        return@setOnClickListener
                    }
                    if (!DateTimeUtil.isOneYearApart(mStartTime, mEndTime)) {
                        ToastUtils.showShort("只允许查询下单时间区间在1年内的订单")
                        return@setOnClickListener
                    }
                }
            }
            func.invoke(mTimeAdapter?.itemId ?: 0, mFastAdapter?.itemId ?: 0, mStartTime, mEndTime)
            dismiss()
        }
    }

    fun setData(timeData: List<OrderFilterItemBean>, quickData: List<OrderFilterItemBean>) {
        mTimeAdapter?.setNewData(timeData)
        mFastAdapter?.setNewData(quickData)
    }

    /**
     * 设置默认选中
     */
    fun setDefault(timeItemId: Int, quickItemId: Int, startTime: String = "", endTime: String = "") {
        mTimeAdapter?.itemId = timeItemId
        mFastAdapter?.itemId = quickItemId
        mStartTime = startTime
        mEndTime = endTime
        initTimeAdapter()
    }

    private fun initTimeAdapter() {
        if (mTimeAdapter?.itemId == 0) {
            if (mStartTime.isNotEmpty()) {
                tvStartTime?.text = mStartTime
                tvStartTime?.isSelected = true
            }
            if (mEndTime.isNotEmpty()) {
                tvEndTime?.text = mEndTime
                tvEndTime?.isSelected = true
            }
        } else {
            clearTime()
        }
    }


    /**
     * 移除选择的日期
     */
    private fun clearTime() {
        tvStartTime?.text = "选择起始时间"
        tvEndTime?.text = "选择终止时间"
        mStartTime = ""
        mEndTime = ""
        tvStartTime?.isSelected = false
        tvEndTime?.isSelected = false
    }

    /**
     * 日期选择
     */
    private fun showTimeSheetDialog(isStart: Boolean) {
        // 默认起始、终止最大时间为当前时间
        val maxDate = Calendar.getInstance()
        // 选择起始时间：起始时间最大值为终止时间
        if (isStart && !mEndTime.isEmpty()) {
            val endTimes = mEndTime.split("-")
            maxDate.set(endTimes[0].toInt(), endTimes[1].toInt() - 1, endTimes[2].toInt(), 23, 59, 59)
        }
        // 最小时间
        val minDate = Calendar.getInstance()
        minDate.set(2024, 0, 1)
        // 选择终止时间：终止时间最小时间为起始时间
        if (!isStart && !mStartTime.isEmpty()) {
            val startTimes = mStartTime.split("-")
            minDate.set(startTimes[0].toInt(), startTimes[1].toInt() - 1, startTimes[2].toInt(), 0, 0, 0)
        }

        PickerManager.showDateSelectPicker(
            activity,
            maxDate, // 默认选中时间（今天或终止时间）
            minDate, // 最小可选时间
            maxDate // 最大可选时间（今天或终止时间）
        ) { date: Date, dataStr: String? ->
            if (dataStr.isNullOrEmpty()) {
                return@showDateSelectPicker
            }
            mTimeAdapter?.itemId = 0
            if (isStart) {
                tvStartTime?.text = dataStr
                tvStartTime?.isSelected = true
                mStartTime = dataStr
            } else {
                tvEndTime?.text = dataStr
                tvEndTime?.isSelected = true
                mEndTime = dataStr
            }
        }
    }


}