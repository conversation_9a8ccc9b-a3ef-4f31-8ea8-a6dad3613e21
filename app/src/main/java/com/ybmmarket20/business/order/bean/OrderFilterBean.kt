package com.ybmmarket20.business.order.bean

/**
 * <AUTHOR>
 * @desc 订单筛选选项
 */
data class OrderFilterBean(
    val itemType: Int,
    val field: String,
    val itemList: List<OrderFilterItemBean>
){
    companion object{
        const val ITEM_TYPE_TIME = 1    // 时间筛选
        const val ITME_TYPE_QUICK = 2   // 快速筛选
    }
}

data class OrderFilterItemBean(
    val itemId: Int,
    val itemText: String
)