package com.ybmmarket20.business.shop.adapter

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import com.ybmmarket20.business.shop.ui.ShopGoodsTabFragment
import com.ybmmarket20.business.shop.ui.ShopHomeTabFragment
import com.ybmmarket20.business.shop.ui.VIRTUAL
import com.ybmmarket20.fragments.ShopSwitchFragment
import com.ybmmarket20.utils.analysis.BaseFlowData

class ShopAllAdapter(
        fm: FragmentManager?,
        orgId: String?,
        shopCode: String?,
        shopPatternCode: String?,
        val source: String? = "",
        val isFilterUnableAddCart: String? = "",
        val mFlowData: BaseFlowData,
        val shopName: String,
        private val mAnchorCsuId: String?,
) :
    FragmentPagerAdapter(fm!!) {

    private val fragments: MutableList<Fragment> = mutableListOf()
    val titles: MutableList<String> = mutableListOf()

    override fun getItem(position: Int): Fragment {
        return fragments[position]
    }

    override fun getCount(): Int {
        return titles.size
    }

    override fun getPageTitle(position: Int): CharSequence? {
        return titles[position]
    }


    init {
        titles.add("首页")
        titles.add("商品")
//        titles.takeIf { VIRTUAL != shopPatternCode }?.add("资质/配送")
        setShopName(shopName)
        fragments.add(ShopHomeTabFragment().apply {
            arguments = Bundle().apply {
                putString("orgId", orgId)
                putString("shopCode", shopCode)
                putString("shopName", <EMAIL>)
                putString("source", source)
                putString("isFilterUnableAddCart", isFilterUnableAddCart)
            }
        })
        fragments.add(ShopGoodsTabFragment().apply {
            arguments = Bundle().apply {
                putString("orgId", orgId)
                putString("shopCode", shopCode)
                putString("anchorCsuId", <EMAIL>)
                putString("source", source)
                putString("isFilterUnableAddCart", isFilterUnableAddCart)
            }
        })
        fragments.takeIf { VIRTUAL != shopPatternCode }?.add(ShopSwitchFragment().apply {
            arguments = Bundle().apply {
                putString("orgId", orgId)
                putString("shopCode", shopCode)
                putString("source", source)
                putString("isFilterUnableAddCart", isFilterUnableAddCart)
            }
        })
        fragments.forEach {
            it.arguments?.apply {
                putString("spType", mFlowData.spType)
                putString("spId", mFlowData.spId)
                putString("sId", mFlowData.sId)
            }
        }
    }

    /**
     * 设置店铺名称
     */
    fun setShopName(shopName: String?) {
        if (fragments.isNotEmpty() && fragments[0] is ShopHomeTabFragment) {
            (fragments[0] as ShopHomeTabFragment).shopName = shopName
        }
    }
}
