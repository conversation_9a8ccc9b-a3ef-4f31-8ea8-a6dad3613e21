package com.ybmmarket20.business.shop.adapter

import android.content.Intent
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.util.SparseArray
import androidx.core.content.ContextCompat
import androidx.core.util.containsKey
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponRowBean
import com.ybmmarket20.business.shop.ui.ShopAllActvity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import org.json.JSONObject

/**
 * <AUTHOR> Brin
 * @date : 2020/11/2 - 14:35
 * @Description :
 * @version
 */
class ShopCouponAdapter(layoutResId: Int, listDada: MutableList<CouponRowBean>, var shopName: String? = "",var shopCode:String?="") : YBMBaseAdapter<CouponRowBean>(layoutResId, listDada) {

    private val exposureViews = SparseArray<String>()

    companion object{
        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: CouponRowBean) {
        if (!exposureViews.containsKey(baseViewHolder.layoutPosition)) {
            exposureViews.put(baseViewHolder.layoutPosition, "${t.id}")
            val obj = JSONObject().apply {
                put("text", shopName?: "")
                put("id", t.templateId)
            }
            XyyIoUtil.track(XyyIoUtil.SHOPHOME_COUPON_EXPOSURE, obj)
        }

        val showMonyInvercher: SpannableStringBuilder =
            if (t.discount > 0) SpannableStringBuilder("${t.moneyInVoucher}折").also {
                it.setSpan(AbsoluteSizeSpan(11, true), it.length - 1, it.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
            }
            else SpannableStringBuilder("¥${t.moneyInVoucher}").also {
                it.setSpan(AbsoluteSizeSpan(11, true), 0, 1, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
            }

        when (t.state) {

            1 -> {
                baseViewHolder.setBackgroundRes(R.id.cl_pop_item, R.drawable.bg_pop_coupon_unreceived)

                baseViewHolder.setTextColor(R.id.tv_coupon_amount, ContextCompat.getColor(mContext, R.color.white))
                baseViewHolder.setText(R.id.tv_coupon_amount, showMonyInvercher)

                baseViewHolder.setTextColor(R.id.tv_coupon_desc, ContextCompat.getColor(mContext, R.color.white))
                baseViewHolder.setText(R.id.tv_coupon_desc, t.minMoneyToEnableDesc)

                baseViewHolder.setTextColor(R.id.tv_coupon_status, ContextCompat.getColor(mContext, R.color.white))
                baseViewHolder.setText(R.id.tv_coupon_status, "领取")
                baseViewHolder.setOnClickListener(R.id.tv_coupon_status) {
                    val obj = JSONObject().apply {
                        put("text", shopName?: "")
                        put("id", t.templateId)
                    }
                    XyyIoUtil.track(XyyIoUtil.SHOPHOME_COUPON_CLICK, obj)
                    t.templateId?.let { getCoupon(it) }
                }
            }
            2 -> {
                baseViewHolder.setBackgroundRes(R.id.cl_pop_item, R.drawable.bg_pop_coupon_received)
                baseViewHolder.setTextColor(R.id.tv_coupon_amount, ContextCompat.getColor(mContext, R.color.color_ff4237))
                baseViewHolder.setText(R.id.tv_coupon_amount, showMonyInvercher)

                baseViewHolder.setTextColor(R.id.tv_coupon_desc, ContextCompat.getColor(mContext, R.color.color_ff4237))
                baseViewHolder.setText(R.id.tv_coupon_desc, t.minMoneyToEnableDesc)

                baseViewHolder.setTextColor(R.id.tv_coupon_status, ContextCompat.getColor(mContext, R.color.color_ff4237))
                baseViewHolder.setText(R.id.tv_coupon_status, "已领取")
                baseViewHolder.setOnClickListener(R.id.tv_coupon_status) { }
            }
        }
    }

    private fun getCoupon(voucherId: String) {
        val params = RequestParams()
        params.put("voucherTemplateId", voucherId)
        params.put("merchantId", SpUtil.getMerchantid())
        HttpManager.getInstance().post(AppNetConfig.RECEIVE_USABLE_VOUCHER, params, object : BaseResponse<BaseBean<*>>() {
            override fun onSuccess(content: String?, obj: BaseBean<BaseBean<*>>?, t: BaseBean<*>?) {
                // 领取优惠券成功, 刷新本页面的优惠券数据
                LocalBroadcastManager.getInstance(mContext).sendBroadcast(Intent(IntentCanst.ACTION_RECEIVE_POP_COUPON))
            }

        })
    }

}